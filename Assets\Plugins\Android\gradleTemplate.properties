org.gradle.jvmargs=-Xmx**JVM_HEAP_SIZE**M
org.gradle.parallel=true
android.enableR8=**MINIFY_WITH_R_EIGHT**
unityStreamingAssets=**STREAMING_ASSETS**

#com.android.tools.build:gradle:7.2.0
#gradle版本7.2需要jdk11
# org.gradle.java.home=D\:\\Android\\jdk11
org.gradle.java.home=C\:\\Users\\Admin\\.jdks\\ms-17.0.15
#ironSource
android.enableDexingArtifactTransform=false

# Android Resolver Properties Start
android.useAndroidX=true
android.enableJetifier=true
# Android Resolver Properties End

# 修复 com.android.tools.r8.kotlin.H 错误
# android.enableR8.fullMode=false
# android.enableR8=false
**ADDITIONAL_PROPERTIES**

#https://discussions.unity.com/t/gradle-build-issues-for-android-api-sdk-35-in-unity-2022-3lts/1502187/5
android.aapt2FromMavenOverride=F\:\\AndroidEnv\\AndroidPlayer-bobo-tw\\SDK\\build-tools\\35.0.0\\aapt2.exe