using System.Collections.Generic;

using FairyGUI;

using UnityEngine;

public class ShopPanel : Panel
{
    public ShopPanel()
    {
        packName = "Shop";
        compName = "ShopPanel";
        modal = true;
    }

    private int OPEN_ITEM_BUNDLE4_LEVEL = 7;
    private GComponent boxContent;
    protected override void DoInitialize()
    {
        GGISdk.Inst.OnPurchaseSuccess += OnPurchaseSuccess;
        GGISdk.Inst.OnPurchaseFail += OnPurchaseFail;
        new CoinBar(contentPane.GetChild("coinBar"));
        boxContent = contentPane.GetChild("boxContent").asCom;
        SystemFacade.PassSystem.CleanExpiredPasses();
        UpdateData();

        var enterTween = contentPane.GetTransition("EnterTween");
        FixUIOffset.FixTransitionY(enterTween, false);
        //boxContent关联imgTitle顶到底，需要减去偏移量修正滚动高度
        boxContent.height -= FixUIOffset.Value;
    }

    private void UpdateData()
    {
        SetNoadsBox("boxNoads", ProductIds.NO_ADS);

        SetItemBundleBox("boxItemBundle1", ProductIds.ITEM_BUNDLE_001);
        SetItemBundleBox("boxItemBundle2", ProductIds.ITEM_BUNDLE_002);
        SetItemBundleBox("boxItemBundle3", ProductIds.ITEM_BUNDLE_003);
        if (GameGlobal.Level >= OPEN_ITEM_BUNDLE4_LEVEL)
        {
            SetItemBundleBox("boxItemBundle4", ProductIds.ITEM_BUNDLE_004);
        }

        SetVipPassBox("boxVipPass1", ProductIds.VIP_PASS_3DAYS);
        SetVipPassBox("boxVipPass2", ProductIds.VIP_PASS_7DAYS);
        SetVipPassBox("boxVipPass3", ProductIds.VIP_PASS_15DAYS);

        SetCoinsBox("listCoin", new string[] { ProductIds.COINS_001, ProductIds.COINS_002,
                                                ProductIds.COINS_003, ProductIds.COINS_004,
                                                ProductIds.COINS_005, ProductIds.COINS_006 });
    }
    private void SetNoadsBox(string compName, string productId)
    {
        var obj = boxContent.GetChild(compName);
        if (obj == null)
            return;

        var infoGoods = ConfigGoods.GetData(productId);
        if (infoGoods == null)
            return;

        // 检查购买次数限制
        if (infoGoods.buyCount > 0 && StorageMgr.GetProductPurchaseCount(productId) >= infoGoods.buyCount)
        {
            obj.visible = false;
            return;
        }

        obj.visible = GameGlobal.HasBannerAndForcedAd;

        var productInfo = GGISdk.Inst.GetProductInfo(productId);
        var btnBuy = obj.asCom.GetChild("btnBuy").asButton;
        btnBuy.text = productInfo != null ? productInfo.localizedPriceString : "----";
        btnBuy.onClick.Set(() =>
        {
            Purchase(productId);
        });
    }
    private void SetItemBundleBox(string compName, string productId)
    {
        var obj = boxContent.GetChild(compName);
        if (obj == null)
            return;

        var infoGoods = ConfigGoods.GetData(productId);
        if (infoGoods == null)
            return;

        // 检查购买次数限制
        if (infoGoods.buyCount > 0 && StorageMgr.GetProductPurchaseCount(productId) >= infoGoods.buyCount)
        {
            obj.visible = false;
            return;
        }

        obj.visible = true;
        var productInfo = GGISdk.Inst.GetProductInfo(productId);

        var lblCount = obj.asCom.GetChild("lblCount").asTextField;
        var btnBuy = obj.asCom.GetChild("btnBuy").asButton;
        var listReward = obj.asCom.GetChild("listReward").asList;
        listReward.itemRenderer = UpdateRewardItem;
        listReward.data = infoGoods.rewards;
        listReward.numItems = infoGoods.rewards.Length;

        lblCount.text = infoGoods.gold.ToString();
        btnBuy.text = productInfo != null ? productInfo.localizedPriceString : "----";
        btnBuy.onClick.Set(() =>
        {
            Purchase(productId);
        });
    }
    private void UpdateRewardItem(int index, GObject item)
    {
        var icon = item.asCom.GetChild("icon").asLoader;
        var lblCount = item.asCom.GetChild("lblCount").asTextField;

        var itemVo = (item.parent.data as ItemVo[])[index];
        var infoItem = ConfigItem.GetData(itemVo.itemId);
        icon.url = infoItem.IconUrl;
        lblCount.text = itemVo.count.ToString();
    }
    private void SetVipPassBox(string compName, string productId)
    {
        var obj = boxContent.GetChild(compName);
        if (obj == null)
            return;

        var infoGoods = ConfigGoods.GetData(productId);
        if (infoGoods == null)
            return;

        // 检查购买次数限制
        if (infoGoods.buyCount > 0 && StorageMgr.GetProductPurchaseCount(productId) >= infoGoods.buyCount)
        {
            obj.visible = false;
            return;
        }

        var c1 = obj.asCom.GetController("c1");//0:未购买 1:已购买未领取 2：已领取
        var btnBuy = obj.asCom.GetChild("btnBuy").asButton;
        var btnClaim = obj.asCom.GetChild("btnClaim").asButton;
        var btnPurReward = obj.asCom.GetChild("btnPurReward").asButton;

        var lblLeftTime = obj.asCom.GetChild("lblLeftTime").asTextField;
        var productInfo = GGISdk.Inst.GetProductInfo(productId);

        if (infoGoods.pass > 0)
        {
            var passSystem = SystemFacade.PassSystem;
            if (passSystem.HasPurchasedPass(infoGoods.pass))
            {
                c1.selectedIndex = passSystem.CanClaimDailyReward(infoGoods.pass) ? 1 : 2;
                lblLeftTime.text = DateUtil.DayHour(passSystem.GetPassRemainingTime(infoGoods.pass));
            }
            else
            {
                c1.selectedIndex = 0;
            }

            btnClaim.onClick.Set(() =>
            {
                ClaimPassDailyReward(infoGoods.pass);
            });
        }

        btnBuy.text = productInfo != null ? productInfo.localizedPriceString : "----";
        btnBuy.onClick.Set(() =>
        {
            Purchase(productId);
        });
        btnPurReward.onClick.Set(() =>
        {
            var pos = btnPurReward.LocalToRoot(new Vector2(btnPurReward.width * 0.5f, btnPurReward.height * 0.5f), GRoot.inst);
            BoxRewardTips.Create(pos, infoGoods.rewards);
        });
    }
    private void SetCoinsBox(string compName, string[] productIds)
    {
        var obj = boxContent.GetChild(compName);
        if (obj == null)
            return;
        var listCoin = obj.asList;
        listCoin.itemRenderer = UpdateCoinItem;
        listCoin.data = productIds;
        listCoin.numItems = productIds.Length;
    }
    private void UpdateCoinItem(int index, GObject item)
    {
        var icon = item.asCom.GetChild("icon").asLoader;
        var lblCount = item.asCom.GetChild("lblCount").asTextField;
        var btnBuy = item.asCom.GetChild("btnBuy").asButton;

        var productIds = item.parent.data as string[];
        var productId = productIds[index];
        var productInfo = GGISdk.Inst.GetProductInfo(productId);

        var infoGoods = ConfigGoods.GetData(productId);

        // 检查购买次数限制
        if (infoGoods.buyCount > 0 && StorageMgr.GetProductPurchaseCount(productId) >= infoGoods.buyCount)
        {
            item.visible = false;
            return;
        }

        lblCount.text = infoGoods.gold.ToString();
        icon.url = GetCurPackRes(infoGoods.icon);

        btnBuy.text = productInfo != null ? productInfo.localizedPriceString : "----";
        btnBuy.onClick.Set(() =>
        {
            Purchase(productId);
        });
    }
    private void Purchase(string productId)
    {
        GGISdk.Inst.Purchase(productId);
    }
    private void OnPurchaseSuccess(string productId)
    {
        Log.Info("productId: " + productId);
        var infoGoods = ConfigGoods.GetData(productId);
        if (infoGoods == null)
            return;

        // 增加商品购买次数记录
        StorageMgr.IncrementProductPurchaseCount(productId);

        if (infoGoods.pass > 0)
        {
            PurchasePass(infoGoods.pass);
        }
        if (infoGoods.noads)
        {
            GameGlobal.HasBannerAndForcedAd = false;
        }
        ClaimRewards(infoGoods.pass, infoGoods.gold, infoGoods.rewards);
        UpdateData();
        StorageMgr.Save();

        SystemFacade.RedPointSystem.TriggerRefresh();
    }
    private void ClaimPassDailyReward(int pass)
    {
        if (pass == 0)
            return;
        var infoPass = ConfigPass.GetData(pass);
        if (infoPass == null)
            return;

        SystemFacade.PassSystem.ClaimDailyReward(pass);
        if (infoPass.dailyRewards.Length > 0)
        {
            for (int i = 0; i < infoPass.dailyRewards.Length; i++)
            {
                var itemVo = infoPass.dailyRewards[i];
                GameGlobal.IncrementItemCount(itemVo.itemId, itemVo.count);
            }
            TipMgr.ShowRewards(infoPass.dailyRewards);
        }
        UpdateData();

        SystemFacade.RedPointSystem.TriggerRefresh();
    }
    private void PurchasePass(int pass)
    {
        var passSystem = SystemFacade.PassSystem;
        passSystem.PurchasePass(pass);
    }
    private void ClaimRewards(int pass, int gold, ItemVo[] rewards)
    {
        var rewardList = new List<ItemVo>();
        if (gold > 0)
        {
            rewardList.Add(new ItemVo(ItemId.ItemGold, gold));
        }
        rewardList.AddRange(rewards);

        if (rewardList.Count == 0)
        {
            return;
        }

        for (int i = 0; i < rewardList.Count; i++)
        {
            var itemVo = rewardList[i];
            GameGlobal.IncrementItemCount(itemVo.itemId, itemVo.count);
        }

        TipMgr.ShowRewards(rewardList.ToArray());
    }
    private void OnPurchaseFail(string message)
    {

    }
    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                Hide();
                break;
        }
    }
    protected override void OnHide()
    {
        GGISdk.Inst.OnPurchaseSuccess -= OnPurchaseSuccess;
        GGISdk.Inst.OnPurchaseFail -= OnPurchaseFail;
    }
}