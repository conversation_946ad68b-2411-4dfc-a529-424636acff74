using UnityEngine;

/// <summary>
/// 系统访问门面类，提供便捷的系统访问方式
/// </summary>
public static class SystemFacade
{
    // 全局系统访问
    public static PassSystem PassSystem => GetGlobalSystem<PassSystem>();
    public static RedPointSystem RedPointSystem => GetGlobalSystem<RedPointSystem>();

    // 玩法系统访问

    /// <summary>
    /// 清理所有缓存的系统引用
    /// </summary>
    public static void ClearGlobalSystemCache()
    {
        // 清理全局系统缓存
        SystemManager.Inst.ClearGlobalSystems();
    }

    /// <summary>
    /// 清理所有玩法系统缓存
    /// </summary>
    public static void ClearGameplaySystemCache()
    {
        SystemManager.Inst.ClearGameplaySystems();
    }

    private static T GetGlobalSystem<T>() where T : GlobalSystem, new()
    {
        return SystemManager.Inst.GetGlobalSystem<T>();
    }

    private static T GetGameplaySystem<T>() where T : GameplaySystem, new()
    {
        return SystemManager.Inst.GetGameplaySystem<T>();
    }
}
