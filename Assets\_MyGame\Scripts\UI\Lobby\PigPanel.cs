using System;
using FairyGUI;

public class PigPanel : Panel
{
    public Action<int> OnGetSuccess;
    public PigPanel()
    {
        packName = "Lobby";
        compName = "PigPanel";
        modal = true;
    }

    private GButton btnGet;
    private GButton btnGo;
    private GProgressBar progressBar;
    protected override void DoInitialize()
    {
        btnGet = contentPane.GetChild("btnGet").asButton;
        // btnGo = contentPane.GetChild("btnGo").asButton;
        progressBar = contentPane.GetChild("progressBar").asProgress;

        var txtAddGold = contentPane.GetChild("txtAddGold").asTextField;
        var txtGetGold = contentPane.GetChild("txtGetGold").asTextField;
        var txtMaxGold = contentPane.GetChild("txtMaxGold").asTextField;
        var txtDesc1 = contentPane.GetChild("txtDesc1").asTextField;
        var txtDesc2 = contentPane.GetChild("txtDesc2").asTextField;
        var setting = ConfigSetting.Setting;
        txtAddGold.text = setting.pigAddGold.ToString();
        txtGetGold.text = setting.pigGetGold.ToString();
        txtMaxGold.text = setting.pigMaxGold.ToString();
        txtDesc1.text = string.Format(txtDesc1.text, setting.pigGetGold, setting.pigMaxGold);
        txtDesc2.text = string.Format(txtDesc2.text, setting.pigAddGold);

        progressBar.max = setting.pigMaxGold;
        progressBar.value = GameGlobal.PigGold;
        if (GameGlobal.PigGold < setting.pigGetGold)
        {
            // btnGo.visible = true;
            btnGet.enabled = false;
        }
        else
        {
            // btnGo.visible = false;
            btnGet.enabled = true;
        }

        GGISdk.Inst.ReportVideoExposure(1);
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                Hide();
                break;
            case "btnGet":
                Platform.Instance.ShowVideoAd(AdType.pig, () =>
                {
                    Hide();
                    var gold = GameGlobal.PigGold;
                    GameGlobal.IncrementGold(gold);
                    GameGlobal.ResetPigGold();
                    OnGetSuccess?.Invoke(gold);

                    SystemFacade.RedPointSystem.TriggerRefresh();
                });
                break;
            case "btnGo":
                Hide();
                new CmdEnterBattle().Execute();
                break;
        }
    }
}