using System;
using FairyGUI;
using UnityEngine;

public class LobbyPanel : Panel
{
    public LobbyPanel()
    {
        packName = "Lobby";
        compName = "LobbyPanel";
    }

    // private GTextField txtLevel;
    private CoinBar coinBar;
    private HeartBar heartBar;
    private BoxLevelBar levelBar;
    private GComponent btnLevelBar;
    private GButton btnPig;
    private GButton btnRemoveAd;
    private GButton btnCloseAd;
    private GButton btnShop;
    private GProgressBar prgPig;
    protected override void DoInitialize()
    {
        // txtLevel = contentPane.GetChild("txtLevel").asTextField;
        // txtLevel.text = LangUtil.GetText("txtLevel", GameGlobal.Level);

        var btnStart = contentPane.GetChild("btnStart").asButton;
        btnStart.text = LangUtil.GetText("txtLevel", GameGlobal.Level);

        coinBar = new CoinBar(contentPane.GetChild("coinBar"));
        heartBar = new HeartBar(contentPane.GetChild("heartBar"));
        // starBar = new BoxStarBar(contentPane.GetChild("starBar"));
        btnLevelBar = contentPane.GetChild("levelBar").asButton;
        levelBar = new BoxLevelBar(btnLevelBar, contentPane);
        btnPig = contentPane.GetChild("btnPig").asButton;
        prgPig = btnPig.GetChild("prgPig").asProgress;
        UpdatePigProgress();
        btnShop = contentPane.GetChild("btnShop").asButton;
        btnRemoveAd = contentPane.GetChild("btnRemoveAd").asButton;
        btnCloseAd = contentPane.GetChild("btnCloseAd").asButton;
        var txtVersion = contentPane.GetChild("txtVersion").asTextField;
        // txtVersion.text = $"ver {GameConfig.GetVer()}";
        GGISdk.Inst.ReportCoinAndHeart();


        btnCloseAd.visible = btnRemoveAd.visible = GameGlobal.HasBannerAndForcedAd;
        GGISdk.Inst.OnPurchaseSuccess += OnPurchased;

        SystemFacade.RedPointSystem.OnNeedRefresh += RefreshRedPoint;
        RefreshRedPoint();
    }

    private void RefreshRedPoint()
    {
        //金猪
        bool hasRedPoint = GameGlobal.PigGold >= ConfigSetting.Setting.pigGetGold;
        EnableButtonRedPoint(btnPig, hasRedPoint);

        //关卡宝箱
        var infoBoxLevel = ConfigBoxLevel.GetReward(GameGlobal.PassLevelIndex);
        if (infoBoxLevel != null)
        {
            hasRedPoint = GameGlobal.PassLevel >= infoBoxLevel.needPassLevel;
            EnableButtonRedPoint(btnLevelBar, hasRedPoint);
        }

        //通行证
        hasRedPoint = CheckPassRedPoint();
        EnableButtonRedPoint(btnShop, hasRedPoint);
    }

    private bool CheckPassRedPoint()
    {
        var passProductIds = new string[]
        {
            ProductIds.VIP_PASS_3DAYS,
            ProductIds.VIP_PASS_7DAYS,
            ProductIds.VIP_PASS_15DAYS
        };

        foreach (var productId in passProductIds)
        {
            var infoGoods = ConfigGoods.GetData(productId);
            if (infoGoods != null && SystemFacade.PassSystem.CanClaimDailyReward(infoGoods.pass))
            {
                return true;
            }
        }
        return false;
    }

    private void EnableButtonRedPoint(GObject btn, bool enable)
    {
        if(btn == null)
            return;

        var redPoint = btn.asButton.GetChild("imgRedDot");
        if (redPoint == null)
            return;

        redPoint.visible = enable;
    }

    private void UpdatePigProgress()
    {
        prgPig.max = ConfigSetting.Setting.pigGetGold;
        prgPig.value = GameGlobal.PigGold;
    }

    private void OnPurchased(string productId)
    {
        switch (productId)
        {
            case ProductIds.NO_ADS:
                GameGlobal.HasBannerAndForcedAd = false;
                btnCloseAd.visible = btnRemoveAd.visible = false;
                GGISdk.Inst.HideBannerAd();
                StorageMgr.Save();
                break;
        }
    }

    protected override void OnShow()
    {
        if (GameGlobal.NeedShowDailyChallenge)
        {
            ShowDailyChallenge();
        }
    }

    protected override void OnHide()
    {
        GGISdk.Inst.OnPurchaseSuccess -= OnPurchased;
        SystemFacade.RedPointSystem.OnNeedRefresh -= RefreshRedPoint;
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnStart":
                // IronSource.Agent.showRewardedVideo();

                if (HeartCooldown.Instance.GetCurrentHearts() > 0)
                {
                    GameGlobal.NeedShowDailyChallenge = false;
                    new CmdEnterBattle().Execute();
                    Hide();
                }
                else
                {
                    ShowBuyHeartPanel();
                }
                break;
            case "coinBar":
                // Create<ShopPanel>();
                break;
            case "heartBar":
                if (HeartCooldown.Instance.IsFull)
                {
                    TipMgr.ShowTip("体力已满！");
                }
                else
                {
                    ShowBuyHeartPanel();
                }
                break;
            case "btnPig":
                Create((PigPanel panel) =>
                {
                    panel.OnGetSuccess = (gold) =>
                    {
                        UIEffectUtil.FlyItem(contentPane, btnPig.xy, ItemId.ItemGold, gold);
                        UpdatePigProgress();
                    };
                });
                break;
            case "btnShop":
                Create<ShopPanel>();
                break;
            case "btnSetting":
                Create((SettingPanel panel) =>
                {
                    panel.OnClosed = (type) =>
                    {
                        if (type == SettingPanel.CloseType_SwitchLanguage)
                        {
                            RestartGame();
                        }
                    };
                    panel.SetData(SettingPanel.SettingViewMode.Setting);
                });
                break;
            case "btnDailyChallenge":
                ShowDailyChallenge();
                break;
            case "btnRank":
                Create<ChallengeRankPanel>();
                break;

            case "btnCloseAd":
            case "btnRemoveAd":
                Create<RemoveAdPanel>();
                break;
        }
    }

    private void RestartGame()
    {
        HidePanel(0);
        FUILoader.UnloadAllPackages();

        LoadingPanel.Show();
        
        ConfigLoader.Inst.LoadConfigs(()=>
        {
            GameRoot.SwitchHandler<LobbyHandler>();
        });
    }

    private void ShowDailyChallenge()
    {
        Create((DailyChallengePanel panel) =>
        {
            panel.OnClosed = (closeType) =>
            {
                if (closeType == DailyChallengePanel.CloseType_EnterBattle)
                {
                    Hide();
                }
            };
        });
    }

    private void ShowBuyHeartPanel()
    {
        Create((BuyHeartPanel panel) =>
        {
            panel.OnBuySuccess = (buyCount) =>
            {
                var startPos = new Vector2(GRoot.inst.width / 2f, GRoot.inst.height / 2f);
                var targetPos = heartBar.GetHeartPos();
                for (int i = 0; i < buyCount; i++)
                {
                    var heart = UIPackage.CreateObject(packName, "Heart");
                    var bornPos = startPos;
                    if (buyCount > 1)
                    {
                        bornPos += UnityEngine.Random.insideUnitCircle * UnityEngine.Random.Range(-150, 150);
                    }
                    heart.xy = startPos;
                    contentPane.AddChild(heart);
                    heart.TweenMove(bornPos, 0.2f).OnComplete(() =>
                    {
                        if (contentPane.isDisposed) return;
                        heart.TweenScale(Vector2.one * 0.4f, 0.5f);
                        heart.TweenMove(targetPos, 0.5f).SetDelay(UnityEngine.Random.Range(0, 0.2f)).SetEase(EaseType.BackIn).OnComplete(() =>
                        {
                            if (contentPane.isDisposed) return;
                            heart.Dispose();
                        });
                    });
                }
            };
        });
    }
}