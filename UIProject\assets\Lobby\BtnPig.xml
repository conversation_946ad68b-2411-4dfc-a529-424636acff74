<?xml version="1.0" encoding="utf-8"?>
<component size="231,120" pivot="0.5,0.5" extention="Button">
  <displayList>
    <movieclip id="n3_gu4g" name="n3" src="gu4g68" fileName="effect/pig/pigEff.jta" xy="-27,-35"/>
    <text id="n2_k64y" name="title" xy="11,79" size="208,33" fontSize="24" color="#ffffff" align="center" vAlign="middle" autoSize="shrink" bold="true" autoClearText="true" text="金猪存钱罐">
      <relation target="" sidePair="width-width,height-height,center-center,middle-middle"/>
    </text>
    <component id="n4_gu4g" name="prgPig" src="gu4g69" fileName="ProgressBarPig.xml" xy="51,53" touchable="false">
      <ProgressBar value="50" max="100"/>
    </component>
    <image id="n5_v6gw" name="imgRedDot" src="v6gw5p" fileName="images/image_redDot2.png" pkg="dzm3l2gb" xy="196,5" pivot="0.5,0.5" visible="false">
      <relation target="" sidePair="right-right,top-top"/>
    </image>
  </displayList>
  <Button downEffect="scale" downEffectValue="0.9"/>
  <transition name="t0" autoPlay="true" autoPlayRepeat="-1">
    <item time="0" type="Rotation" target="n5_v6gw" tween="true" startValue="0" endValue="-20" duration="2" ease="Linear"/>
    <item time="2" type="Rotation" target="n5_v6gw" tween="true" startValue="-20" endValue="20" duration="2" ease="Linear"/>
    <item time="4" type="Rotation" target="n5_v6gw" tween="true" startValue="20" endValue="-20" duration="2" ease="Linear"/>
    <item time="6" type="Rotation" target="n5_v6gw" tween="true" startValue="-20" endValue="20" duration="2" ease="Linear"/>
    <item time="8" type="Rotation" target="n5_v6gw" tween="true" startValue="20" endValue="0" duration="1" ease="Linear"/>
    <item time="32" type="Rotation" target="n5_v6gw" value="0"/>
  </transition>
</component>