// Android Resolver Repos Start
([rootProject] + (rootProject.subprojects as List)).each { project ->
    project.repositories {
        def unityProjectPath = $/file:///**DIR_UNITYPROJECT**/$.replace("\\", "/")
        maven {
            url "https://maven.google.com"
        }
        maven {
            url (unityProjectPath + "/Assets/GeneratedLocalRepo/Firebase/m2repository") // Assets/Firebase/Editor/AnalyticsDependencies.xml:18, Assets/Firebase/Editor/AppDependencies.xml:22
        }
        maven {
            url "https://cboost.jfrog.io/artifactory/chartboost-ads/" // Packages/com.applovin.mediation.adapters.chartboost.android/Editor/Dependencies.xml:8
        }
        maven {
            url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea" // Packages/com.applovin.mediation.adapters.mintegral.android/Editor/Dependencies.xml:8
        }
        maven {
            url "https://artifact.bytedance.com/repository/pangle" // Packages/com.applovin.mediation.adapters.bytedance.android/Editor/Dependencies.xml:8
        }
        mavenLocal()
        mavenCentral()
    }
}
// Android Resolver Repos End
apply plugin: 'com.android.library'
**APPLY_PLUGINS**

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'
    implementation 'com.android.installreferrer:installreferrer:2.2'
// Android Resolver Dependencies Start
    implementation 'androidx.recyclerview:recyclerview:1.2.1' // Packages/com.applovin.mediation.adapters.mintegral.android/Editor/Dependencies.xml:9
    implementation 'com.android.support:appcompat-v7:25.3.1' // Facebook.Unity.Editor.AndroidSupportLibraryResolver.addSupportLibraryDependency
    implementation 'com.android.support:cardview-v7:25.3.1' // Facebook.Unity.Editor.AndroidSupportLibraryResolver.addSupportLibraryDependency
    // implementation 'com.android.support:customtabs:25.3.1' // Facebook.Unity.Editor.AndroidSupportLibraryResolver.addSupportLibraryDependency
    implementation 'com.android.support:customtabs:28.+' // Packages/com.applovin.mediation.adapters.inmobi.android/Editor/Dependencies.xml:7
    implementation 'com.android.support:recyclerview-v7:28.+' // Packages/com.applovin.mediation.adapters.inmobi.android/Editor/Dependencies.xml:6
    implementation 'com.android.support:support-v4:25.3.1' // Facebook.Unity.Editor.AndroidSupportLibraryResolver.addSupportLibraryDependency
    implementation 'com.applovin.mediation:bigoads-adapter:5.3.0.1' // Packages/com.applovin.mediation.adapters.bigoads.android/Editor/Dependencies.xml:4
    implementation 'com.applovin.mediation:bytedance-adapter:7.1.0.8.0' // Packages/com.applovin.mediation.adapters.bytedance.android/Editor/Dependencies.xml:8
    implementation 'com.applovin.mediation:chartboost-adapter:9.8.3.0' // Packages/com.applovin.mediation.adapters.chartboost.android/Editor/Dependencies.xml:8
    implementation 'com.applovin.mediation:facebook-adapter:[6.20.0.0]' // Packages/com.applovin.mediation.adapters.facebook.android/Editor/Dependencies.xml:7
    implementation 'com.applovin.mediation:fyber-adapter:8.3.7.0' // Packages/com.applovin.mediation.adapters.fyber.android/Editor/Dependencies.xml:4
    implementation 'com.applovin.mediation:google-adapter:[24.2.0.1]' // Packages/com.applovin.mediation.adapters.google.android/Editor/Dependencies.xml:4
    implementation 'com.applovin.mediation:inmobi-adapter:10.8.3.0' // Packages/com.applovin.mediation.adapters.inmobi.android/Editor/Dependencies.xml:4
    implementation 'com.applovin.mediation:ironsource-adapter:8.8.0.0.1' // Packages/com.applovin.mediation.adapters.ironsource.android/Editor/Dependencies.xml:4
    implementation 'com.applovin.mediation:mintegral-adapter:16.9.71.0' // Packages/com.applovin.mediation.adapters.mintegral.android/Editor/Dependencies.xml:8
    implementation 'com.applovin.mediation:moloco-adapter:3.9.0.0' // Packages/com.applovin.mediation.adapters.moloco.android/Editor/Dependencies.xml:4
    implementation 'com.applovin.mediation:unityads-adapter:4.14.2.0' // Packages/com.applovin.mediation.adapters.unityads.android/Editor/Dependencies.xml:4
    implementation 'com.applovin.mediation:vungle-adapter:7.5.0.0' // Packages/com.applovin.mediation.adapters.vungle.android/Editor/Dependencies.xml:4
    implementation 'com.applovin:applovin-sdk:13.2.0' // Packages/com.applovin.mediation.ads/AppLovin/Editor/Dependencies.xml:4
    implementation 'com.facebook.android:facebook-applinks:[17.0.0,18)' // Assets/FacebookSDK/Plugins/Editor/Dependencies.xml:6
    implementation 'com.facebook.android:facebook-core:[17.0.0,18)' // Assets/FacebookSDK/Plugins/Editor/Dependencies.xml:5
    implementation 'com.facebook.android:facebook-gamingservices:[17.0.0,18)' // Assets/FacebookSDK/Plugins/Editor/Dependencies.xml:9
    implementation 'com.facebook.android:facebook-login:[17.0.0,18)' // Assets/FacebookSDK/Plugins/Editor/Dependencies.xml:7
    implementation 'com.facebook.android:facebook-share:[17.0.0,18)' // Assets/FacebookSDK/Plugins/Editor/Dependencies.xml:8
    implementation 'com.google.android.gms:play-services-ads-identifier:18.1.0' // Assets/Editor/Dependencies.xml:7
    implementation 'com.google.android.gms:play-services-appset:16.0.2' // Assets/GameAnalytics/Editor/Android/Dependencies.xml:4
    // implementation 'com.google.android.gms:play-services-base:16.1.0' // Packages/com.applovin.mediation.adapters.chartboost.android/Editor/Dependencies.xml:9
    implementation 'com.google.android.gms:play-services-base:18.6.0' // Assets/Firebase/Editor/AppDependencies.xml:17
    implementation 'com.google.android.instantapps:instantapps:1.1.0' // Assets/GameAnalytics/Editor/Android/Dependencies.xml:3
    implementation 'com.google.android.play:core-common:2.0.4' // Assets/GooglePlayPlugins/com.google.play.core/Editor/Dependencies.xml:3
    implementation 'com.google.android.play:review:2.0.2' // Assets/GooglePlayPlugins/com.google.play.review/Editor/Dependencies.xml:3
    implementation 'com.google.firebase:firebase-analytics:22.4.0' // Assets/Firebase/Editor/AppDependencies.xml:15
    implementation 'com.google.firebase:firebase-analytics-unity:12.8.0' // Assets/Firebase/Editor/AnalyticsDependencies.xml:18
    implementation 'com.google.firebase:firebase-app-unity:12.8.0' // Assets/Firebase/Editor/AppDependencies.xml:22
    implementation 'com.google.firebase:firebase-common:21.0.0' // Assets/Firebase/Editor/AppDependencies.xml:13
    implementation 'com.parse.bolts:bolts-android:1.4.0' // Assets/FacebookSDK/Plugins/Editor/Dependencies.xml:4
    implementation 'com.squareup.picasso:picasso:2.71828' // Packages/com.applovin.mediation.adapters.inmobi.android/Editor/Dependencies.xml:5
    implementation 'com.tenjin:android-sdk:1.16.6' // Assets/Editor/Dependencies.xml:4
// Android Resolver Dependencies End
**DEPS**}

// Android Resolver Exclusions Start
android {
  packagingOptions {
      exclude ('/lib/armeabi/*' + '*')
      exclude ('/lib/mips/*' + '*')
      exclude ('/lib/mips64/*' + '*')
      exclude ('/lib/x86/*' + '*')
      exclude ('/lib/x86_64/*' + '*')
  }
}
// Android Resolver Exclusions End
android {
    compileSdkVersion **APIVERSION**
    buildToolsVersion '**BUILDTOOLS**'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion **MINSDKVERSION**
        targetSdkVersion **TARGETSDKVERSION**
        ndk {
            abiFilters **ABIFILTERS**
        }
        versionCode **VERSIONCODE**
        versionName '**VERSIONNAME**'
        consumerProguardFiles 'proguard-unity.txt'**USER_PROGUARD**
    }

    lintOptions {
        abortOnError false
    }

    aaptOptions {
        noCompress = **BUILTIN_NOCOMPRESS** + unityStreamingAssets.tokenize(', ')
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"
    }**PACKAGING_OPTIONS**
}**REPOSITORIES**
**IL_CPP_BUILD_SETUP**
**SOURCE_BUILD_SETUP**
**EXTERNAL_SOURCES**
