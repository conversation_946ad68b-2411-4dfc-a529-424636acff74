<dependencies>
  <packages>
    <package>androidx.recyclerview:recyclerview:1.2.1</package>
    <package>com.android.support:appcompat-v7:25.3.1</package>
    <package>com.android.support:cardview-v7:25.3.1</package>
    <package>com.android.support:customtabs:25.3.1</package>
    <package>com.android.support:customtabs:28.+</package>
    <package>com.android.support:recyclerview-v7:28.+</package>
    <package>com.android.support:support-v4:25.3.1</package>
    <package>com.applovin.mediation:bigoads-adapter:5.3.0.1</package>
    <package>com.applovin.mediation:bytedance-adapter:7.1.0.8.0</package>
    <package>com.applovin.mediation:chartboost-adapter:9.8.3.0</package>
    <package>com.applovin.mediation:facebook-adapter:[6.20.0.0]</package>
    <package>com.applovin.mediation:fyber-adapter:8.3.7.0</package>
    <package>com.applovin.mediation:google-adapter:[24.2.0.1]</package>
    <package>com.applovin.mediation:inmobi-adapter:10.8.3.0</package>
    <package>com.applovin.mediation:ironsource-adapter:8.8.0.0.1</package>
    <package>com.applovin.mediation:mintegral-adapter:16.9.71.0</package>
    <package>com.applovin.mediation:moloco-adapter:3.9.0.0</package>
    <package>com.applovin.mediation:unityads-adapter:4.14.2.0</package>
    <package>com.applovin.mediation:vungle-adapter:7.5.0.0</package>
    <package>com.applovin:applovin-sdk:13.2.0</package>
    <package>com.facebook.android:facebook-applinks:[17.0.0,18)</package>
    <package>com.facebook.android:facebook-core:[17.0.0,18)</package>
    <package>com.facebook.android:facebook-gamingservices:[17.0.0,18)</package>
    <package>com.facebook.android:facebook-login:[17.0.0,18)</package>
    <package>com.facebook.android:facebook-share:[17.0.0,18)</package>
    <package>com.google.android.gms:play-services-ads-identifier:18.1.0</package>
    <package>com.google.android.gms:play-services-appset:16.0.2</package>
    <package>com.google.android.gms:play-services-base:16.1.0</package>
    <package>com.google.android.gms:play-services-base:18.6.0</package>
    <package>com.google.android.instantapps:instantapps:1.1.0</package>
    <package>com.google.android.play:core-common:2.0.4</package>
    <package>com.google.android.play:review:2.0.2</package>
    <package>com.google.firebase:firebase-analytics:22.4.0</package>
    <package>com.google.firebase:firebase-analytics-unity:12.8.0</package>
    <package>com.google.firebase:firebase-app-unity:12.8.0</package>
    <package>com.google.firebase:firebase-common:21.0.0</package>
    <package>com.parse.bolts:bolts-android:1.4.0</package>
    <package>com.squareup.picasso:picasso:2.71828</package>
    <package>com.tenjin:android-sdk:1.16.6</package>
  </packages>
  <files>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-analytics-unity/12.8.0/firebase-analytics-unity-12.8.0.aar</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-analytics-unity/12.8.0/firebase-analytics-unity-12.8.0.pom</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-app-unity/12.8.0/firebase-app-unity-12.8.0.aar</file>
    <file>Assets/GeneratedLocalRepo/Firebase/m2repository/com/google/firebase/firebase-app-unity/12.8.0/firebase-app-unity-12.8.0.pom</file>
  </files>
  <settings>
    <setting name="androidAbis" value="arm64-v8a,armeabi-v7a" />
    <setting name="bundleId" value="com.zplay.mjpung" />
    <setting name="explodeAars" value="True" />
    <setting name="gradleBuildEnabled" value="True" />
    <setting name="gradlePropertiesTemplateEnabled" value="True" />
    <setting name="gradleTemplateEnabled" value="True" />
    <setting name="installAndroidPackages" value="True" />
    <setting name="localMavenRepoDir" value="Assets/GeneratedLocalRepo" />
    <setting name="packageDir" value="Assets/Plugins/Android" />
    <setting name="patchAndroidManifest" value="True" />
    <setting name="patchMainTemplateGradle" value="True" />
    <setting name="projectExportEnabled" value="False" />
    <setting name="useFullCustomMavenRepoPathWhenExport" value="True" />
    <setting name="useFullCustomMavenRepoPathWhenNotExport" value="False" />
    <setting name="useJetifier" value="True" />
  </settings>
</dependencies>