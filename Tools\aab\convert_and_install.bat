@echo off
chcp 65001 >nul

REM 设置工作目录为脚本所在目录
cd /d "%~dp0"

REM 设置adb路径
set ADB_PATH="D:\Android\AndroidPlayer20210314\SDK\platform-tools\adb.exe"
set KEYSTORE_PATH="../sign/user.keystore"
set KEYSTORE_ALIAS="key0"
set KEYSTORE_PASS="wudx1234"
set AAB_PATH="../../Release/apk/pung0809.aab"

REM 下载bundletool
if not exist "bundletool.jar" (
    echo Downloading bundletool...
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/google/bundletool/releases/download/1.15.6/bundletool-all-1.15.6.jar' -OutFile 'bundletool.jar'"
)

REM 检查adb
%ADB_PATH% version >nul 2>&1
if %errorlevel% neq 0 (
    echo adb not found at %ADB_PATH%
    pause
    exit /b 1
)

REM 显示连接的设备
echo.
echo 当前连接的设备列表：
%ADB_PATH% devices -l

REM 删除已存在的APKS文件
if exist "pung.apks" del /f "pung.apks"

REM 转换AAB到APK
echo 开始转换AAB到APK...
java -jar bundletool.jar build-apks --bundle=%AAB_PATH% --output=pung.apks --mode=universal --ks=%KEYSTORE_PATH% --ks-pass=pass:%KEYSTORE_PASS% --ks-key-alias=%KEYSTORE_ALIAS% --key-pass=pass:%KEYSTORE_PASS% 2>&1
echo 转换完成
echo.

REM 解压APKS文件
if exist "pung_apks" rmdir /s /q "pung_apks"
mkdir "pung_apks"
powershell -Command "& { Add-Type -A 'System.IO.Compression.FileSystem'; [IO.Compression.ZipFile]::ExtractToDirectory('pung.apks', 'pung_apks'); }"

REM 安装APK
@REM %ADB_PATH% install-multiple "pung_apks/universal.apk"

REM 启动应用
@REM %ADB_PATH% shell am start -n com.zplay.mjpung/com.unity3d.player.UnityPlayerActivity

pause
