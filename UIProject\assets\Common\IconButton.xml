<?xml version="1.0" encoding="utf-8"?>
<component size="100,60" pivot="0.5,0.5" extention="Button">
  <displayList>
    <loader id="n1_p6dv" name="icon" xy="0,0" size="100,60" touchable="false" align="center" vAlign="middle">
      <relation target="" sidePair="width-width,height-height"/>
    </loader>
    <text id="n2_k64y" name="title" xy="0,6" size="100,47" fontSize="20" color="#ffffff" align="center" vAlign="middle" autoSize="shrink" text="">
      <relation target="" sidePair="width-width,height-height,center-center,middle-middle"/>
    </text>
    <image id="n3_v6gw" name="imgRedDot" src="v6gw5p" fileName="images/image_redDot2.png" xy="57,-2" pivot="0.5,0.5" visible="false">
      <relation target="" sidePair="right-right,top-top"/>
    </image>
  </displayList>
  <Button downEffect="scale" downEffectValue="0.9"/>
  <transition name="t0" autoPlay="true" autoPlayRepeat="-1">
    <item time="0" type="Rotation" target="n3_v6gw" tween="true" startValue="0" endValue="-20" duration="2" ease="Linear"/>
    <item time="2" type="Rotation" target="n3_v6gw" tween="true" startValue="-20" endValue="20" duration="2" ease="Linear"/>
    <item time="4" type="Rotation" target="n3_v6gw" tween="true" startValue="20" endValue="-20" duration="2" ease="Linear"/>
    <item time="6" type="Rotation" target="n3_v6gw" tween="true" startValue="-20" endValue="20" duration="2" ease="Linear"/>
    <item time="8" type="Rotation" target="n3_v6gw" tween="true" startValue="20" endValue="0" duration="1" ease="Linear"/>
    <item time="32" type="Rotation" target="n3_v6gw" value="0"/>
  </transition>
</component>